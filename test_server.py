#!/usr/bin/env python3
"""
Test script for the Movie & TV MCP Server
Run this to test all functionality before deployment
"""

import asyncio
import os
import json
from movie_server import mcp

async def test_all_tools():
    """Test all MCP tools with sample data"""

    print("Testing Movie & TV MCP Server")
    print("=" * 50)

    # Check if API key is configured
    api_key = os.getenv("TMDB_API_KEY")
    if not api_key:
        print("ERROR: TMDB_API_KEY not set. Please set your API key first.")
        print("Get your API key from: https://www.themoviedb.org/settings/api")
        return

    print(f"SUCCESS: API Key configured: {api_key[:8]}...")
    print()

    # Test cases
    test_cases = [
        {
            "name": "Search Movies - The Matrix",
            "tool": "search_movies",
            "args": {"query": "The Matrix", "year": 1999}
        },
        {
            "name": "Search TV Shows - Breaking Bad",
            "tool": "search_tv_shows",
            "args": {"query": "Breaking Bad"}
        },
        {
            "name": "Movie Details - The Matrix",
            "tool": "get_movie_details",
            "args": {"movie_id": 603}
        },
        {
            "name": "TV Show Details - Breaking Bad",
            "tool": "get_tv_show_details",
            "args": {"tv_id": 1396}
        },
        {
            "name": "Trending Movies This Week",
            "tool": "get_trending",
            "args": {"media_type": "movie", "time_window": "week"}
        },
        {
            "name": "Discover Action Movies",
            "tool": "discover_content",
            "args": {"content_type": "movie", "genre_id": 28, "sort_by": "vote_average.desc"}
        }
    ]

    # Test resources
    resource_tests = [
        {
            "name": "API Configuration",
            "resource": "config://movie-api"
        },
        {
            "name": "Popular Genres",
            "resource": "data://popular-genres"
        },
        {
            "name": "Usage Examples",
            "resource": "help://usage-examples"
        }
    ]

    # Run tool tests
    print("Testing Tools:")
    print("-" * 30)

    for test in test_cases:
        print(f"Testing: {test['name']}")
        try:
            # Get the tool function
            tool_func = getattr(mcp._tools[test['tool']], 'func')

            # Call the tool (pass None for context since we're testing)
            result = await tool_func(None, **test['args'])

            # Parse the JSON result
            data = json.loads(result)

            if data.get("success"):
                if "results" in data:
                    count = len(data["results"]) if data["results"] else 0
                    print(f"  SUCCESS - {count} results")
                else:
                    print(f"  SUCCESS - Details retrieved")
            else:
                print(f"  FAILED - {data.get('error', 'Unknown error')}")

        except Exception as e:
            print(f"  ERROR - {str(e)}")

        print()

    # Test resources
    print("Testing Resources:")
    print("-" * 30)

    for test in resource_tests:
        print(f"Testing: {test['name']}")
        try:
            # Get the resource function
            resource_func = getattr(mcp._resources[test['resource']], 'func')

            # Call the resource
            result = await resource_func()

            # Parse the JSON result
            data = json.loads(result)

            if isinstance(data, dict):
                print(f"  SUCCESS - Resource data retrieved")
            else:
                print(f"  FAILED - Invalid response format")

        except Exception as e:
            print(f"  ERROR - {str(e)}")

        print()

    print("Testing completed!")
    print()
    print("Next steps:")
    print("1. If all tests passed, your server is ready for deployment")
    print("2. Create a GitHub repository and upload all files")
    print("3. Deploy to Smithery with your TMDb API key")
    print("4. Test with real MCP clients")

async def test_image_urls():
    """Test image URL construction"""
    print("Testing Image URL Construction:")
    print("-" * 40)

    # Test movie search to get real image paths
    try:
        from movie_server import search_movies
        result = await search_movies(None, "The Matrix", 1999)
        data = json.loads(result)

        if data.get("success") and data.get("results"):
            movie = data["results"][0]
            print(f"Movie: {movie['title']}")
            print("Poster URLs:")
            for size, url in movie["poster_urls"].items():
                if url:
                    print(f"  {size}: {url}")
            print()
            print("Backdrop URLs:")
            for size, url in movie["backdrop_urls"].items():
                if url:
                    print(f"  {size}: {url}")
        else:
            print("ERROR: Could not retrieve movie data for image testing")

    except Exception as e:
        print(f"ERROR: Error testing image URLs: {str(e)}")

if __name__ == "__main__":
    print("Movie & TV MCP Server Test Suite")
    print("=" * 50)
    print()

    # Run all tests
    asyncio.run(test_all_tools())
    print()
    asyncio.run(test_image_urls())
